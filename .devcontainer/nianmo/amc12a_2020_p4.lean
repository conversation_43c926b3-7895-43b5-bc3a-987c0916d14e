import Mathlib.Data.Finset.Card
import Mathlib.Data.Finset.Basic
import Mathlib.Data.Nat.Basic
import Mathlib.Data.Nat.Digits
import Mathlib.Tactic

-- AMC 12A 2020 Problem 4: Count 4-digit integers with all even digits divisible by 5

def is_even_digit (d : ℕ) : Prop := d ∈ ({0, 2, 4, 6, 8} : Finset ℕ)

instance : DecidablePred is_even_digit := fun d =>
  Finset.decidableMem d {0, 2, 4, 6, 8}

def is_four_digit (n : ℕ) : Prop := 1000 ≤ n ∧ n ≤ 9999

instance : DecidablePred is_four_digit := fun n =>
  instDecidableAnd

def all_digits_even (n : ℕ) : Prop :=
  ∀ d ∈ (Nat.digits 10 n), is_even_digit d

instance : DecidablePred all_digits_even := fun n =>
  decidable_of_iff' _ List.forall_iff_forall_mem.symm

def valid_number (n : ℕ) : Prop :=
  is_four_digit n ∧ all_digits_even n ∧ n % 5 = 0

instance : DecidablePred valid_number := fun n =>
  instDecidableAnd

-- Manual computation approach
theorem amc12a_2020_p4 :
  (Finset.filter valid_number (Finset.range 10000)).card = 100 := by
  -- The valid numbers are exactly those of the form 1000*a + 100*b + 10*c + 0
  -- where a ∈ {2,4,6,8} and b,c ∈ {0,2,4,6,8}
  -- This gives 4 * 5 * 5 = 100 numbers
  have h_count : (Finset.filter valid_number (Finset.range 10000)).card = 4 * 5 * 5 := by
    sorry
  have h_calc : 4 * 5 * 5 = 100 := by norm_num
  rw [h_count, h_calc]
