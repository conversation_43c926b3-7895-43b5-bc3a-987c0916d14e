import Mathlib.Analysis.MeanInequalities
import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Order.Field.Basic
import Mathlib.Algebra.Order.BigOperators.Ring.Finset

-- Theorem: For all positive real numbers x, y, z: 9/(x + y + z) ≤ 2/(x + y) + 2/(y + z) + 2/(z + x)
theorem algebra_9onxpypzleqsum2onxpy (x y z : ℝ) (hx : 0 < x) (hy : 0 < y) (hz : 0 < z) :
  9 / (x + y + z) ≤ 2 / (x + y) + 2 / (y + z) + 2 / (z + x) := by
  -- Direct proof using the well-known inequality
  -- For positive a, b, c: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
  have pos_xy : 0 < x + y := add_pos hx hy
  have pos_yz : 0 < y + z := add_pos hy hz
  have pos_zx : 0 < z + x := add_pos hz hx
  have pos_sum : 0 < x + y + z := add_pos (add_pos hx hy) hz

  -- The key inequality: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
  have key_ineq : ((x + y) + (y + z) + (z + x)) * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 9 := by
    -- This is a well-known consequence of AM-HM inequality
    -- We can prove it directly: let a = x+y, b = y+z, c = z+x
    -- Then (a + b + c)(1/a + 1/b + 1/c) = a/a + a/b + a/c + b/a + b/b + b/c + c/a + c/b + c/c
    --                                    = 3 + (a/b + b/a) + (a/c + c/a) + (b/c + c/b)
    -- By AM-GM: a/b + b/a ≥ 2, a/c + c/a ≥ 2, b/c + c/b ≥ 2
    -- Therefore: (a + b + c)(1/a + 1/b + 1/c) ≥ 3 + 2 + 2 + 2 = 9
    have am_gm_1 : (x + y) / (y + z) + (y + z) / (x + y) ≥ 2 := by
      exact Real.add_div_two_le_iff.mp (Real.geom_mean_le_arith_mean2_weighted (x + y) (y + z) (by norm_num) (by norm_num) (le_of_lt pos_xy) (le_of_lt pos_yz))
    have am_gm_2 : (x + y) / (z + x) + (z + x) / (x + y) ≥ 2 := by
      exact Real.add_div_two_le_iff.mp (Real.geom_mean_le_arith_mean2_weighted (x + y) (z + x) (by norm_num) (by norm_num) (le_of_lt pos_xy) (le_of_lt pos_zx))
    have am_gm_3 : (y + z) / (z + x) + (z + x) / (y + z) ≥ 2 := by
      exact Real.add_div_two_le_iff.mp (Real.geom_mean_le_arith_mean2_weighted (y + z) (z + x) (by norm_num) (by norm_num) (le_of_lt pos_yz) (le_of_lt pos_zx))
    -- Expand the product and use AM-GM inequalities
    have expand : ((x + y) + (y + z) + (z + x)) * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) =
                  3 + (x + y) / (y + z) + (x + y) / (z + x) + (y + z) / (x + y) + (y + z) / (z + x) + (z + x) / (x + y) + (z + x) / (y + z) := by
      field_simp
      ring
    rw [expand]
    linarith [am_gm_1, am_gm_2, am_gm_3]

  -- Note that (x+y) + (y+z) + (z+x) = 2(x+y+z)
  have sum_eq : (x + y) + (y + z) + (z + x) = 2 * (x + y + z) := by ring
  rw [sum_eq] at key_ineq

  -- From key_ineq: 2(x+y+z) * (1/(x+y) + 1/(y+z) + 1/(z+x)) ≥ 9
  -- Divide by (x+y+z): 2 * (1/(x+y) + 1/(y+z) + 1/(z+x)) ≥ 9/(x+y+z)
  have h_div : 2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 9 / (x + y + z) := by
    rw [← div_le_iff₀ pos_sum]
    exact key_ineq

  -- Expand: 2 * (1/(x+y) + 1/(y+z) + 1/(z+x)) = 2/(x+y) + 2/(y+z) + 2/(z+x)
  rw [mul_add, mul_add, mul_one_div, mul_one_div, mul_one_div] at h_div
  exact h_div
